package com.yd.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.common.utils.Bytes;
import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.processor.Processor;
import org.apache.kafka.streams.processor.ProcessorContext;
import org.apache.kafka.streams.processor.PunctuationType;
import org.apache.kafka.streams.processor.TimestampExtractor;
import org.apache.kafka.streams.state.WindowStore;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class DemoTest {
    private static final String BOOTSTRAP_SERVERS = "localhost:9092";
    private static final String TOPIC = "demoTest";

    public static void main(String[] args) throws ExecutionException, InterruptedException {
//        write();
        read();
    }

    /**
     * Kafka 生产者方法 - 向 Kafka 主题发送测试消息
     *
     * 功能说明：
     * 1. 创建并配置 Kafka 生产者
     * 2. 循环发送 10 条 JSON 格式的测试消息
     * 3. 每条消息包含时间戳和 IP 地址信息
     * 4. 同步等待每条消息的发送确认
     * 5. 关闭生产者并输出统计信息
     *
     * @throws InterruptedException 当线程被中断时抛出
     * @throws ExecutionException 当异步操作执行失败时抛出
     */
    public static void write() throws InterruptedException, ExecutionException {
        // 第一步：创建生产者配置属性对象
        Properties props = new Properties();

        // 配置 Kafka 集群地址 - 指定要连接的 Kafka broker 服务器地址
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);

        // 配置消息键（Key）的序列化器 - 将 Java 对象转换为字节数组以便网络传输
        // 这里使用 StringSerializer 将字符串键序列化为字节数组
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        // 配置消息值（Value）的序列化器 - 将消息内容序列化为字节数组
        // 这里使用 StringSerializer 将字符串消息序列化为字节数组
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());

        // 第二步：使用配置创建 Kafka 生产者实例
        // 泛型 <String, String> 表示消息的键和值都是字符串类型
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        // 第三步：循环发送测试数据
        long count = 0; // 消息计数器，用于统计发送的消息数量

        for (int i = 0; i < 10; i++) {
            // 获取当前时间戳（毫秒）- 用作消息的时间标识和键值
            long t = System.currentTimeMillis();

            // 构造 JSON 格式的消息内容
            // 包含时间戳和模拟的 IP 地址信息
            String data = String.format("{\"timestamp\":%d, \"ip\":\"***********\"}", t);

            // 创建 Kafka 消息记录并发送
            // ProducerRecord 构造参数：(主题名, 消息键, 消息值)
            // - TOPIC: 目标主题名称 "demoTest"
            // - String.valueOf(t): 使用时间戳作为消息键，用于分区路由
            // - data: JSON 格式的消息内容
            Future<RecordMetadata> f = producer.send(
                new org.apache.kafka.clients.producer.ProducerRecord<>(TOPIC, String.valueOf(t), data)
            );

            // 同步等待消息发送完成并获取元数据信息
            // f.get() 会阻塞当前线程直到消息发送成功或失败
            // RecordMetadata 包含分区号、偏移量等信息
            System.out.println((++count) + " 发送反馈:" + f.get());

            // 暂停 100 毫秒，控制消息发送频率
            // 避免过快发送消息对 Kafka 造成压力
            Thread.sleep(100);
        }

        // 第四步：关闭生产者，释放资源
        // 这会确保所有待发送的消息都被发送完毕
        producer.close();

        // 输出发送完成的统计信息
        System.out.println("生产者测试完成，共发送 " + count + " 条消息");
    }

    /**
     * Kafka Streams 消费者方法 - 实时流处理和窗口聚合
     *
     * 功能概述：
     * 1. 从 Kafka 主题消费消息流
     * 2. 解析 JSON 消息，提取 IP 地址作为分组键
     * 3. 使用滑动时间窗口进行 IP 访问次数聚合
     * 4. 实时输出每个 IP 在时间窗口内的访问统计
     * 5. 通过定时器监控流处理的时间进度
     *
     * 核心概念：
     * - 时间窗口：1分钟窗口大小，每30秒滑动一次
     * - 流处理拓扑：消息流 -> 解析转换 -> 分组 -> 窗口聚合 -> 结果输出
     * - 状态存储：使用 RocksDB 本地存储窗口聚合状态
     */
    public static void read() {
        // ==================== 第一步：Kafka Streams 配置 ====================
        Properties props = new Properties();

        // 应用程序唯一标识符 - 用于消费者组管理和状态存储目录命名
        // 相同 APPLICATION_ID 的多个实例会形成一个消费者组，实现负载均衡
        props.put(StreamsConfig.APPLICATION_ID_CONFIG, "demo-test-consumer-xzw-1");

        // Kafka 集群地址配置 - 指定要连接的 Kafka broker 服务器
        props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);

        // 默认键序列化/反序列化器配置 - 处理消息键的数据转换
        // Serdes.String() 提供字符串与字节数组之间的双向转换
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass());

        // 默认值序列化/反序列化器配置 - 处理消息值的数据转换
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, Serdes.String().getClass());

        // 禁用缓存 - 设置为0表示立即处理每条消息，不进行批量缓存
        // 这样可以获得最低延迟，但会降低吞吐量
        props.put(StreamsConfig.CACHE_MAX_BYTES_BUFFERING_CONFIG, 0);

        // 自定义时间戳提取器 - 从消息中提取事件时间用于时间窗口计算
        // CustomTimestampExtractor 会从消息的时间戳字段提取事件发生时间
        props.put(StreamsConfig.DEFAULT_TIMESTAMP_EXTRACTOR_CLASS_CONFIG, CustomTimestampExtractor.class);

        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        // ==================== 第二步：构建流处理拓扑 ====================

        // 创建流构建器 - 用于定义数据流处理的拓扑结构
        StreamsBuilder builder = new StreamsBuilder();

        // 创建源数据流 - 从指定的 Kafka 主题消费消息
        // KStream 表示无界的、连续的数据流，每条消息都会被处理
        KStream<String, String> source = builder.stream(TOPIC);

        // JSON 解析器 - 用于解析消息中的 JSON 格式数据
        ObjectMapper objectMapper = new ObjectMapper();

        // 消息计数器相关配置（用于测试场景）
        final int recordAllCount = 37706;  // 预期的总消息数量
        final AtomicInteger recordCounter = new AtomicInteger(0);  // 原子计数器，线程安全

        // ==================== 关键配置：Materialized 状态存储 ====================
        /**
         * Materialized 配置详解：
         *
         * 作用：定义窗口聚合操作的状态存储方式和序列化配置
         *
         * 核心功能：
         * 1. 状态持久化：将聚合结果存储到本地 RocksDB 数据库
         * 2. 容错恢复：应用重启时可以从存储中恢复状态
         * 3. 查询支持：支持交互式查询（Interactive Queries）
         * 4. 数据序列化：定义键值的序列化方式
         *
         * 配置参数说明：
         * - as("window-store")：指定状态存储的名称，用于标识和查询
         * - withKeySerde(Serdes.String())：窗口键的序列化器（IP地址字符串）
         * - withValueSerde(Serdes.Long())：聚合值的序列化器（计数Long类型）
         * - WindowStore<Bytes, byte[]>：底层存储类型，使用字节数组存储
         *
         * 存储结构：
         * - 键：窗口化的IP地址 (Windowed<String>)
         * - 值：该IP在窗口时间内的访问次数 (Long)
         *
         * 物理存储：
         * - 本地文件系统中的 RocksDB 数据库
         * - 路径通常为：/tmp/kafka-streams/{APPLICATION_ID}/rocksdb/window-store
         */
        Materialized<String, Long, WindowStore<Bytes, byte[]>> windowMt =
            Materialized.<String, Long, WindowStore<Bytes, byte[]>>as("window-store")
                .withKeySerde(Serdes.String())    // IP地址键的序列化器
                .withValueSerde(Serdes.Long());   // 计数值的序列化器
        // ==================== 第三步：流处理管道构建 ====================

        source
                // ========== 步骤1：消息转换和键重新分配 ==========
                /**
                 * map 操作详解：
                 *
                 * 功能：将原始消息转换为新的键值对
                 * 输入：(原始键, JSON消息) -> 例如：("1734441815123", "{\"timestamp\":1734441815123,\"ip\":\"***********\"}")
                 * 输出：(IP地址, JSON消息) -> 例如：("***********", "{\"timestamp\":1734441815123,\"ip\":\"***********\"}")
                 *
                 * 目的：
                 * 1. 提取IP地址作为新的消息键，用于后续的分组操作
                 * 2. 保持原始JSON消息作为值，供后续处理使用
                 * 3. 统计处理的消息数量
                 *
                 * 数据流转换：
                 * 原始流：KStream<String, String> (时间戳键, JSON值)
                 *   ↓ map转换
                 * 新流：KStream<String, String> (IP键, JSON值)
                 */
                .map((key, value) -> {
                    try {
                        // 解析JSON消息，提取数据字段
                        Map<String, Object> data = objectMapper.readValue(value, Map.class);

                        // 从JSON中提取IP地址字段
                        String ip = (String) data.get("ip");

                        // 返回新的键值对：IP作为键，原始JSON作为值
                        // 这样后续的分组操作就会按IP地址进行分组
                        return KeyValue.pair(ip, value);

                    } catch (IOException e) {
                        // JSON解析失败时抛出运行时异常
                        throw new RuntimeException("Failed to parse JSON", e);
                    } finally {
                        // 无论成功失败都会执行的计数逻辑
                        int count = recordCounter.incrementAndGet();
                        if (count == recordAllCount) {
                            // 当处理完所有预期消息时的标记（测试用）
//                            System.out.println("===================消费完毕==================");
                        }
                    }
                })
                // ========== 步骤2：按键分组 ==========
                /**
                 * groupByKey 操作详解：
                 *
                 * 功能：将具有相同键的消息分组到一起
                 * 输入：KStream<String, String> (IP键, JSON值)
                 * 输出：KGroupedStream<String, String> (按IP分组的流)
                 *
                 * 分组逻辑：
                 * - 相同IP地址的所有消息会被分组到同一个分组中
                 * - 例如：所有来自"***********"的消息会在一个组中
                 *
                 * 分区影响：
                 * - 相同键的消息会被路由到同一个分区
                 * - 保证了同一IP的消息在同一个处理实例中处理
                 */
                .groupByKey()

                // ========== 步骤3：时间窗口定义 ==========
                /**
                 * windowedBy 时间窗口详解：
                 *
                 * 窗口类型：滑动时间窗口 (Sliding Time Window)
                 * 窗口大小：1分钟 (Duration.ofMinutes(1))
                 * 滑动间隔：30秒 (Duration.ofSeconds(30))
                 *
                 * 工作机制：
                 * 1. 每30秒创建一个新的1分钟窗口
                 * 2. 窗口之间有重叠，每条消息可能属于多个窗口
                 * 3. 窗口基于消息的事件时间（而非处理时间）
                 *
                 * 窗口示例（假设当前时间为12:00:00）：
                 * - 窗口1: [11:59:00 - 12:00:00]
                 * - 窗口2: [11:59:30 - 12:00:30] (30秒后创建)
                 * - 窗口3: [12:00:00 - 12:01:00] (再30秒后创建)
                 *
                 * 消息归属：
                 * - 12:00:15的消息会同时属于窗口2和窗口3
                 * - 这样可以提供更平滑的统计结果
                 */
                .windowedBy(TimeWindows.of(Duration.ofMinutes(1)))

                // ========== 步骤4：窗口聚合操作 ==========
                /**
                 * aggregate 聚合操作详解：
                 *
                 * 功能：在每个时间窗口内对同一IP的消息进行计数聚合
                 *
                 * 参数说明：
                 * 1. 初始化器 () -> Long.valueOf(0)：
                 *    - 为每个新窗口提供初始聚合值
                 *    - 每个IP在每个窗口中的计数从0开始
                 *
                 * 2. 聚合器 (key, value, agg) -> agg + 1：
                 *    - key: IP地址 (String)
                 *    - value: JSON消息内容 (String)
                 *    - agg: 当前聚合值 (Long) - 该IP在当前窗口的计数
                 *    - 返回值: 新的聚合值 (agg + 1)
                 *
                 * 3. Materialized配置：windowMt
                 *    - 定义状态存储的配置和序列化方式
                 *
                 * 聚合逻辑：
                 * - 每当一个IP的消息到达某个窗口时，该IP在该窗口的计数+1
                 * - 不同窗口中的同一IP计数是独立的
                 * - 不同IP的计数也是独立的
                 *
                 * 性能模拟：
                 * - Thread.sleep(10) 模拟聚合操作的处理耗时
                 * - 在实际应用中应该移除这个延迟
                 */
                .aggregate(
                        // 初始化器：为每个新的窗口-IP组合提供初始计数值0
                        () -> Long.valueOf(0),

                        // 聚合器：每收到一条消息，对应IP在对应窗口的计数+1
                        (key, value, agg) -> {
                            // 模拟聚合操作的处理耗时（仅用于演示，生产环境应移除）
                            try {
                                Thread.sleep(10);
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                            // 计数器递增：当前聚合值 + 1
                            return agg + 1;
                        },

                        // 状态存储配置：定义如何持久化聚合结果
                        windowMt
                )
                // ========== 步骤5：结果抑制策略 ==========
                /**
                 * suppress 抑制操作详解：
                 *
                 * 功能：控制聚合结果的输出时机，避免中间结果的频繁输出
                 *
                 * 抑制策略：Suppressed.untilWindowCloses()
                 * - 只有当时间窗口完全关闭时才输出最终聚合结果
                 * - 避免窗口期间每次更新都输出中间结果
                 * - 确保输出的是窗口的最终统计数据
                 *
                 * 缓冲配置：BufferConfig.unbounded()
                 * - 使用无界缓冲区存储待输出的结果
                 * - 适用于结果数量不会过大的场景
                 *
                 * 工作原理：
                 * 1. 窗口期间：聚合结果在内存中累积，不对外输出
                 * 2. 窗口关闭：一次性输出该窗口的所有最终聚合结果
                 * 3. 延迟换准确：牺牲实时性，获得准确的窗口统计
                 *
                 * 输出时机示例：
                 * - 窗口[12:00:00-12:01:00]在12:01:00时关闭并输出结果
                 * - 窗口[12:00:30-12:01:30]在12:01:30时关闭并输出结果
                 */
                .suppress(Suppressed.untilWindowCloses(Suppressed.BufferConfig.unbounded()))

                // ========== 步骤6：转换为流并添加自定义处理器 ==========
                /**
                 * toStream 转换说明：
                 * 将 KTable<Windowed<String>, Long> 转换为 KStream<Windowed<String>, Long>
                 *
                 * 数据类型变化：
                 * - 输入：KTable (窗口化的聚合表)
                 * - 输出：KStream (窗口化的聚合流)
                 *
                 * 键类型：Windowed<String>
                 * - 包含IP地址和时间窗口信息的复合键
                 * - 可以通过 key.key() 获取IP地址
                 * - 可以通过 key.window() 获取时间窗口信息
                 *
                 * 值类型：Long
                 * - 该IP在该时间窗口内的访问次数
                 */
                .toStream().process(() -> new Processor<Windowed<String>, Long>() {
                    /**
                     * 自定义处理器初始化方法
                     *
                     * 功能：
                     * 1. 处理器启动时的初始化逻辑
                     * 2. 设置定时器监控流处理的时间进度
                     * 3. 跟踪流时间的变化情况
                     */
                    @Override
                    public void init(ProcessorContext context) {
                        System.out.println("Processor.init");

                        // 记录上次的流时间，用于检测时间是否有进展
                        final AtomicLong lastStreamTime = new AtomicLong(context.currentStreamTimeMs());

                        /**
                         * 定时器配置详解：
                         *
                         * 调度间隔：30秒 (Duration.ofSeconds(30))
                         * 定时器类型：WALL_CLOCK_TIME (墙钟时间)
                         * - 基于系统时间触发，不依赖消息的事件时间
                         * - 即使没有消息流入也会定期触发
                         *
                         * 定时器功能：
                         * 1. 监控流处理的时间进度
                         * 2. 检测流时间是否有变化（是否有新消息处理）
                         * 3. 提供流处理健康状态的监控信息
                         *
                         * 时间类型说明：
                         * - timestamp: 定时器触发的系统时间
                         * - streamTime: 流处理的事件时间（基于消息时间戳）
                         *
                         * 监控逻辑：
                         * - 如果流时间有变化：说明有新消息被处理
                         * - 如果流时间无变化：说明没有新消息或处理停滞
                         */
                        context.schedule(Duration.ofSeconds(30), PunctuationType.WALL_CLOCK_TIME, (timestamp) -> {
                            long oldStreamTime = lastStreamTime.get();  // 上次记录的流时间
                            long newStreamTime = context.currentStreamTimeMs();  // 当前的流时间

                            // 获取当前处理器的分区信息
                            String partitionInfo = "";
                            try {
                                // 获取任务ID，包含分区信息
                                String taskId = context.taskId().toString();
                                partitionInfo = " taskId:" + taskId;
                            } catch (Exception e) {
                                partitionInfo = " taskId:unknown";
                            }

                            // 如果两个时间都无效（<=0），则跳过检查
                            if (oldStreamTime <= 0 && newStreamTime <= 0) {
                                return;
                            }

                            // 检查流时间是否有变化
                            if (oldStreamTime != newStreamTime) {
                                // 流时间有变化，说明有新消息被处理
                                System.out.println("定时器. 时间变化。timestamp:" + fullFmt(timestamp) +
                                    " lastStreamTime:" + fullFmt(oldStreamTime) +
                                    " newStreamTime:" + fullFmt(newStreamTime) +
                                    partitionInfo);
                                lastStreamTime.set(newStreamTime);  // 更新记录的流时间
                            } else {
                                // 流时间无变化，说明没有新消息或处理停滞
                                System.out.println("定时器. 时间没有变化。timestamp:" + fullFmt(timestamp) +
                                    " lastStreamTime:" + fullFmt(oldStreamTime) +
                                    " newStreamTime:" + fullFmt(newStreamTime) +
                                    partitionInfo);
                            }
                        });
                    }

                    /**
                     * 核心处理方法 - 处理每个窗口的聚合结果
                     *
                     * 参数说明：
                     * @param key Windowed<String> - 窗口化的键，包含IP地址和时间窗口信息
                     *            - key.key(): 获取IP地址 (String)
                     *            - key.window(): 获取时间窗口信息 (Window)
                     * @param value Long - 该IP在该时间窗口内的访问次数
                     *
                     * 触发时机：
                     * - 当时间窗口关闭时（由于suppress配置）
                     * - 输出该窗口内每个IP的最终统计结果
                     *
                     * 输出示例：
                     * "窗口变动或关闭.ip:***********,count:5"
                     * 表示IP *********** 在某个1分钟窗口内访问了5次
                     */
                    @Override
                    public void process(Windowed<String> key, Long value) {
                        System.out.println("窗口变动或关闭.ip:" + key.key() + ",count:" + value);
                    }

                    /**
                     * 处理器关闭方法
                     *
                     * 功能：处理器关闭时的清理逻辑
                     * 当前实现为空，可以在此添加资源清理代码
                     */
                    @Override
                    public void close() {
                        // 处理器关闭时的清理逻辑（当前为空）
                    }
                });

        // ==================== 第四步：启动 Kafka Streams 应用 ====================

        /**
         * KafkaStreams 应用启动详解：
         *
         * 构建拓扑：builder.build()
         * - 将之前定义的所有流处理操作构建成完整的拓扑图
         * - 拓扑图描述了数据如何在各个处理节点间流动
         *
         * 创建应用实例：new KafkaStreams(topology, props)
         * - 使用构建的拓扑和配置属性创建流处理应用
         * - 此时应用还未开始处理数据
         *
         * 启动应用：streams.start()
         * - 启动所有处理线程
         * - 开始从Kafka主题消费消息
         * - 初始化状态存储
         * - 开始执行流处理逻辑
         *
         * 应用生命周期：
         * 1. 初始化：连接Kafka，创建消费者，初始化状态存储
         * 2. 运行：持续消费消息，执行流处理逻辑，维护状态
         * 3. 关闭：优雅关闭（需要手动调用streams.close()）
         *
         * 注意事项：
         * - 应用会持续运行直到手动停止
         * - 在生产环境中应该添加优雅关闭的逻辑
         * - 可以通过streams.state()监控应用状态
         */
        KafkaStreams streams = new KafkaStreams(builder.build(), props);
        streams.start();

        // 注意：在生产环境中，应该添加优雅关闭的逻辑
        // Runtime.getRuntime().addShutdownHook(new Thread(streams::close));
    }

    /**
     * 自定义时间戳提取器
     *
     * 功能：从Kafka消息中提取事件时间，用于时间窗口计算
     *
     * 时间类型说明：
     * 1. 事件时间 (Event Time)：消息实际发生的时间
     * 2. 处理时间 (Processing Time)：消息被处理的时间
     * 3. 摄入时间 (Ingestion Time)：消息进入Kafka的时间
     *
     * 本实现使用消息的时间戳作为事件时间
     */
    public static class CustomTimestampExtractor implements TimestampExtractor {
        /**
         * 提取时间戳方法
         *
         * @param record Kafka消息记录，包含消息的所有元数据
         * @param previousTime 前一条消息的时间戳（用于时间回退处理）
         * @return 提取的事件时间戳（毫秒）
         *
         * 提取逻辑：
         * 1. 优先使用消息自带的时间戳（record.timestamp()）
         * 2. 如果消息时间戳无效（<=0），则使用前一条消息的时间
         * 3. 这样可以保证时间的单调性，避免时间回退问题
         *
         * 时间戳来源：
         * - 生产者发送消息时设置的时间戳
         * - 如果生产者未设置，Kafka broker会自动添加当前时间
         */
        @Override
        public long extract(ConsumerRecord<Object, Object> record, long previousTime) {
            // 检查消息是否包含有效的时间戳
            if (record.timestamp() > 0) {
                return record.timestamp();  // 使用消息的时间戳
            }
            return previousTime;  // 使用前一条消息的时间戳
        }
    }

    // ==================== 时间格式化工具方法 ====================

    /**
     * 线程安全的时间格式化器
     *
     * 使用ThreadLocal确保每个线程都有自己的SimpleDateFormat实例
     * 避免多线程环境下SimpleDateFormat的线程安全问题
     */

    // 时分秒毫秒格式：HH:mm:ss.SSS (例如：14:30:25.123)
    private final static ThreadLocal<SimpleDateFormat> DT_TL =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("HH:mm:ss.SSS"));

    // 月日时分秒格式：MMdd HH:mm:ss (例如：1217 14:30:25)
    private final static ThreadLocal<SimpleDateFormat> DT_FULL_TL =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("MMdd HH:mm:ss"));

    // 年月日格式：yyyy-MM-dd (例如：2024-12-17)
    private final static ThreadLocal<SimpleDateFormat> DT_YMD_TL =
        ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    /**
     * 格式化时间戳为时分秒毫秒格式
     * @param tm 时间戳（毫秒）
     * @return 格式化后的时间字符串 (HH:mm:ss.SSS)
     */
    public static String fmt(long tm) {
        return DT_TL.get().format(new Date(tm));
    }

    /**
     * 格式化时间戳为月日时分秒格式
     * @param tm 时间戳（毫秒）
     * @return 格式化后的时间字符串 (MMdd HH:mm:ss)
     */
    public static String fullFmt(long tm) {
        return DT_FULL_TL.get().format(new Date(tm));
    }

    /**
     * 格式化时间戳为年月日格式
     * @param tm 时间戳（毫秒）
     * @return 格式化后的日期字符串 (yyyy-MM-dd)
     */
    public static String ymd(long tm) {
        return DT_YMD_TL.get().format(new Date(tm));
    }
}