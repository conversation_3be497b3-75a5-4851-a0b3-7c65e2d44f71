package com.yd.test;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.serialization.StringSerializer;
import org.apache.kafka.common.utils.Bytes;
import org.apache.kafka.streams.KafkaStreams;
import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.processor.Processor;
import org.apache.kafka.streams.processor.ProcessorContext;
import org.apache.kafka.streams.processor.PunctuationType;
import org.apache.kafka.streams.processor.TimestampExtractor;
import org.apache.kafka.streams.state.WindowStore;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class DemoTest {
    private static final String BOOTSTRAP_SERVERS = "localhost:9092";
    private static final String TOPIC = "demoTest";

    public static void main(String[] args) throws ExecutionException, InterruptedException {
        write();
//        read();
    }

    public static void write() throws InterruptedException, ExecutionException {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);
        // 发送测试数据
        long count = 0;
        for (int i = 0; i < 10; i++) {
            long t = System.currentTimeMillis();
            String data = String.format("{\"timestamp\":%d, \"ip\":\"***********\"}", t);
            Future<RecordMetadata> f = producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(TOPIC, String.valueOf(t), data));
            System.out.println((++count) + " 发送反馈:" + f.get());
            Thread.sleep(100);
        }
        producer.close();
        System.out.println("生产者测试完成，共发送 " + count + " 条消息");
    }

    public static void read() {
        Properties props = new Properties();
        props.put(StreamsConfig.APPLICATION_ID_CONFIG, "demo-test-consumer-0617-4");
        props.put(StreamsConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
        props.put(StreamsConfig.DEFAULT_KEY_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.DEFAULT_VALUE_SERDE_CLASS_CONFIG, Serdes.String().getClass());
        props.put(StreamsConfig.CACHE_MAX_BYTES_BUFFERING_CONFIG, 0);
        props.put(StreamsConfig.DEFAULT_TIMESTAMP_EXTRACTOR_CLASS_CONFIG, CustomTimestampExtractor.class);

        StreamsBuilder builder = new StreamsBuilder();
        KStream<String, String> source = builder.stream(TOPIC);

        ObjectMapper objectMapper = new ObjectMapper();

        final int recordAllCount = 37706;
        final AtomicInteger recordCounter = new AtomicInteger(0);
        Materialized<String, Long, WindowStore<Bytes, byte[]>> windowMt = Materialized.<String, Long, WindowStore<Bytes, byte[]>>as("window-store")
                .withKeySerde(Serdes.String())
                .withValueSerde(Serdes.Long());
        source
                .map((key, value) -> {
                    try {
                        Map<String, Object> data = objectMapper.readValue(value, Map.class);
                        String ip = (String) data.get("ip");
                        return KeyValue.pair(ip, value);
                    } catch (IOException e) {
                        throw new RuntimeException("Failed to parse JSON", e);
                    } finally {
                        int count = recordCounter.incrementAndGet();
                        if (count == recordAllCount) {
//                            System.out.println("===================消费完毕==================");
                        }
                    }
                })
                .groupByKey()
                .windowedBy(TimeWindows.of(Duration.ofMinutes(1)).advanceBy(Duration.ofSeconds(30)))
                .aggregate(
                        () -> Long.valueOf(0),
                        (key, value, agg) -> {
                            //演示聚合执行的耗时
                            try {
                                Thread.sleep(10);
                            } catch (InterruptedException e) {
                                throw new RuntimeException(e);
                            }
                            return agg + 1;
                        }
                        , windowMt
                )
                .suppress(Suppressed.untilWindowCloses(Suppressed.BufferConfig.unbounded()))
                .toStream().process(() -> new Processor<Windowed<String>, Long>() {
                    @Override
                    public void init(ProcessorContext context) {
                        System.out.println("Processor.init");
                        final AtomicLong lastStreamTime = new AtomicLong(context.currentStreamTimeMs());
                        context.schedule(Duration.ofSeconds(30), PunctuationType.WALL_CLOCK_TIME, (timestamp) -> {
                            long oldStreamTime = lastStreamTime.get();
                            long newStreamTime = context.currentStreamTimeMs();
                            if (oldStreamTime <= 0 && newStreamTime <= 0) {
                                return;
                            }
                            if (oldStreamTime != newStreamTime) {
                                System.out.println("定时器. 时间变化。timestamp:" + fullFmt(timestamp) + " lastStreamTime:" + fullFmt(oldStreamTime) + " newStreamTime:" + fullFmt(newStreamTime));
                                lastStreamTime.set(newStreamTime);
                            } else {
                                System.out.println("定时器. 时间没有变化。timestamp:" + fullFmt(timestamp) + " lastStreamTime:" + fullFmt(oldStreamTime) + " newStreamTime:" + fullFmt(newStreamTime));
                            }
                        });
                    }

                    @Override
                    public void process(Windowed<String> key, Long value) {
                        System.out.println("窗口变动或关闭.ip:" + key.key() + ",count:" + value);
                    }

                    @Override
                    public void close() {

                    }
                });

        KafkaStreams streams = new KafkaStreams(builder.build(), props);
        streams.start();
    }

    public static class CustomTimestampExtractor implements TimestampExtractor {
        @Override
        public long extract(ConsumerRecord<Object, Object> record, long previousTime) {
            if (record.timestamp() > 0) {
                return record.timestamp();
            }
            return previousTime;
        }
    }

    private final static ThreadLocal<SimpleDateFormat> DT_TL = ThreadLocal.withInitial(() -> new SimpleDateFormat("HH:mm:ss.SSS"));
    private final static ThreadLocal<SimpleDateFormat> DT_FULL_TL = ThreadLocal.withInitial(() -> new SimpleDateFormat("MMdd HH:mm:ss"));
    private final static ThreadLocal<SimpleDateFormat> DT_YMD_TL = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    public static String fmt(long tm) {
        return DT_TL.get().format(new Date(tm));
    }

    public static String fullFmt(long tm) {
        return DT_FULL_TL.get().format(new Date(tm));
    }

    public static String ymd(long tm) {
        return DT_YMD_TL.get().format(new Date(tm));
    }
}